{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule, NbCheckboxModule, NbSelectModule, NbOptionModule, NbInputModule } from '@nebular/theme';\nlet RequestItemImportComponent = class RequestItemImportComponent {\n  constructor(requirementService, dialogRef) {\n    this.requirementService = requirementService;\n    this.dialogRef = dialogRef;\n    this.buildCaseId = 0;\n    this.houseType = [];\n    this.itemsImported = new EventEmitter();\n    this.currentStep = 1;\n    this.requirements = [];\n    this.loading = false;\n    // 搜尋相關屬性\n    this.searchRequest = {};\n  }\n  ngOnInit() {\n    this.initializeSearchForm();\n    this.loadRequirementsFromAPI();\n  }\n  // 初始化搜尋表單\n  initializeSearchForm() {\n    this.searchRequest.CStatus = 1; // 預設只顯示啟用的項目\n    this.searchRequest.CIsShow = null; // 預設只顯示需要顯示的項目\n    this.searchRequest.CIsSimple = null;\n    this.searchRequest.CRequirement = '';\n    this.searchRequest.CLocation = '';\n    // 使用外部傳入的參數\n    this.searchRequest.CBuildCaseID = this.buildCaseId;\n    this.searchRequest.CHouseType = this.houseType;\n  }\n  // 搜尋事件\n  onSearch() {\n    this.loadRequirementsFromAPI();\n  }\n  // 重置搜尋\n  resetSearch() {\n    this.initializeSearchForm();\n    this.loadRequirementsFromAPI();\n  }\n  loadRequirementsFromAPI() {\n    if (!this.searchRequest.CBuildCaseID) {\n      return;\n    }\n    this.loading = true;\n    // 準備API請求參數\n    const getRequirementListArgs = {\n      CBuildCaseID: this.searchRequest.CBuildCaseID,\n      CHouseType: this.searchRequest.CHouseType,\n      CLocation: this.searchRequest.CLocation || null,\n      CRequirement: this.searchRequest.CRequirement || null,\n      CStatus: this.searchRequest.CStatus,\n      CIsShow: this.searchRequest.CIsShow,\n      CIsSimple: this.searchRequest.CIsSimple,\n      PageIndex: 1,\n      PageSize: 100\n    };\n    this.requirementService.apiRequirementGetRequestListForTemplatePost$Json({\n      body: getRequirementListArgs\n    }).subscribe({\n      next: response => {\n        this.loading = false;\n        if (response.StatusCode === 0 && response.Entries) {\n          this.requirements = response.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n        } else {\n          this.requirements = [];\n        }\n      },\n      error: () => {\n        this.loading = false;\n        this.requirements = [];\n      }\n    });\n  }\n  onRequirementItemChange() {\n    // 當需求項目選擇變更時的處理\n  }\n  getSelectedItems() {\n    return this.requirements.filter(item => item.selected);\n  }\n  canProceed() {\n    switch (this.currentStep) {\n      case 1:\n        return this.getSelectedItems().length > 0;\n      case 2:\n        return true;\n      default:\n        return false;\n    }\n  }\n  nextStep() {\n    if (this.canProceed() && this.currentStep < 2) {\n      this.currentStep++;\n    }\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  getProgressText() {\n    const progressTexts = {\n      1: '請選擇要匯入的需求項目',\n      2: '確認匯入詳情'\n    };\n    return progressTexts[this.currentStep] || '';\n  }\n  importRequirements() {\n    const config = {\n      buildCaseId: this.searchRequest.CBuildCaseID || 0,\n      buildCaseName: '',\n      selectedItems: this.getSelectedItems(),\n      totalItems: this.getSelectedItems().length,\n      searchCriteria: {\n        CHouseType: this.searchRequest.CHouseType || undefined,\n        CLocation: this.searchRequest.CLocation || undefined,\n        CRequirement: this.searchRequest.CRequirement || undefined\n      }\n    };\n    this.itemsImported.emit(config);\n    this.close();\n  }\n  close() {\n    this.resetSelections();\n    this.dialogRef.close();\n  }\n  resetSelections() {\n    this.currentStep = 1;\n    this.requirements.forEach(requirement => {\n      requirement.selected = false;\n    });\n  }\n  selectAll() {\n    const allSelected = this.requirements.every(item => item.selected);\n    this.requirements.forEach(item => {\n      item.selected = !allSelected;\n    });\n  }\n  getSelectedCount() {\n    return this.getSelectedItems().length;\n  }\n  getTotalCount() {\n    return this.requirements.length;\n  }\n};\n__decorate([Input()], RequestItemImportComponent.prototype, \"buildCaseId\", void 0);\n__decorate([Input()], RequestItemImportComponent.prototype, \"houseType\", void 0);\n__decorate([Output()], RequestItemImportComponent.prototype, \"itemsImported\", void 0);\nRequestItemImportComponent = __decorate([Component({\n  selector: 'app-request-item-import',\n  standalone: true,\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule, NbIconModule, NbCheckboxModule, NbSelectModule, NbOptionModule, NbInputModule],\n  templateUrl: './request-item-import.component.html',\n  styleUrls: ['./request-item-import.component.scss']\n})], RequestItemImportComponent);\nexport { RequestItemImportComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Input", "Output", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "NbCheckboxModule", "NbSelectModule", "NbOptionModule", "NbInputModule", "RequestItemImportComponent", "constructor", "requirementService", "dialogRef", "buildCaseId", "houseType", "itemsImported", "currentStep", "requirements", "loading", "searchRequest", "ngOnInit", "initializeSearchForm", "loadRequirementsFromAPI", "CStatus", "CIsShow", "CIsSimple", "CRequirement", "CLocation", "CBuildCaseID", "CHouseType", "onSearch", "resetSearch", "getRequirementListArgs", "PageIndex", "PageSize", "apiRequirementGetRequestListForTemplatePost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "selected", "error", "onRequirementItemChange", "getSelectedItems", "filter", "canProceed", "length", "nextStep", "previousStep", "getProgressText", "progressTexts", "importRequirements", "config", "buildCaseName", "selectedItems", "totalItems", "searchCriteria", "undefined", "emit", "close", "resetSelections", "for<PERSON>ach", "requirement", "selectAll", "allSelected", "every", "getSelectedCount", "getTotalCount", "__decorate", "selector", "standalone", "imports", "NbIconModule", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\request-item-import\\request-item-import.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport {\r\n  NbCardModule,\r\n  NbButtonModule,\r\n  NbCheckboxModule,\r\n  NbSelectModule,\r\n  NbOptionModule,\r\n  NbInputModule,\r\n  NbDialogRef\r\n} from '@nebular/theme';\r\nimport { RequirementService } from 'src/services/api/services/requirement.service';\r\nimport { GetListRequirementRequest, GetRequirement, GetRequirementListResponseBase } from 'src/services/api/models';\r\n\r\nexport interface ExtendedRequirementItem extends GetRequirement {\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface RequestItemImportConfig {\r\n  buildCaseId: number;\r\n  buildCaseName?: string;\r\n  selectedItems: ExtendedRequirementItem[];\r\n  totalItems: number;\r\n  searchCriteria?: {\r\n    CHouseType?: number[];\r\n    CLocation?: string;\r\n    CRequirement?: string;\r\n  };\r\n}\r\n\r\n@Component({\r\n  selector: 'app-request-item-import',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    NbCardModule,\r\n    NbButtonModule,\r\n    NbIconModule,\r\n    NbCheckboxModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NbInputModule\r\n  ],\r\n  templateUrl: './request-item-import.component.html',\r\n  styleUrls: ['./request-item-import.component.scss']\r\n})\r\nexport class RequestItemImportComponent implements OnInit {\r\n  @Input() buildCaseId: number = 0;\r\n  @Input() houseType: number[] = [];\r\n  @Output() itemsImported = new EventEmitter<RequestItemImportConfig>();\r\n\r\n  currentStep: number = 1;\r\n  requirements: ExtendedRequirementItem[] = [];\r\n  loading: boolean = false;\r\n\r\n  // 搜尋相關屬性\r\n  searchRequest: GetListRequirementRequest & { CIsShow?: boolean | null; CIsSimple?: boolean | null } = {};\r\n\r\n  constructor(\r\n    private requirementService: RequirementService,\r\n    private dialogRef: NbDialogRef<RequestItemImportComponent>\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.initializeSearchForm();\r\n    this.loadRequirementsFromAPI();\r\n  }\r\n\r\n  // 初始化搜尋表單\r\n  initializeSearchForm() {\r\n    this.searchRequest.CStatus = 1; // 預設只顯示啟用的項目\r\n    this.searchRequest.CIsShow = null; // 預設只顯示需要顯示的項目\r\n    this.searchRequest.CIsSimple = null;\r\n    this.searchRequest.CRequirement = '';\r\n    this.searchRequest.CLocation = '';\r\n    // 使用外部傳入的參數\r\n    this.searchRequest.CBuildCaseID = this.buildCaseId;\r\n    this.searchRequest.CHouseType = this.houseType;\r\n  }\r\n\r\n\r\n\r\n  // 搜尋事件\r\n  onSearch() {\r\n    this.loadRequirementsFromAPI();\r\n  }\r\n\r\n  // 重置搜尋\r\n  resetSearch() {\r\n    this.initializeSearchForm();\r\n    this.loadRequirementsFromAPI();\r\n  }\r\n\r\n  loadRequirementsFromAPI() {\r\n    if (!this.searchRequest.CBuildCaseID) {\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n\r\n    // 準備API請求參數\r\n    const getRequirementListArgs: GetListRequirementRequest = {\r\n      CBuildCaseID: this.searchRequest.CBuildCaseID,\r\n      CHouseType: this.searchRequest.CHouseType,\r\n      CLocation: this.searchRequest.CLocation || null,\r\n      CRequirement: this.searchRequest.CRequirement || null,\r\n      CStatus: this.searchRequest.CStatus,\r\n      CIsShow: this.searchRequest.CIsShow,\r\n      CIsSimple: this.searchRequest.CIsSimple,\r\n      PageIndex: 1,\r\n      PageSize: 100\r\n    };\r\n\r\n    this.requirementService.apiRequirementGetRequestListForTemplatePost$Json({\r\n      body: getRequirementListArgs\r\n    }).subscribe({\r\n      next: (response: GetRequirementListResponseBase) => {\r\n        this.loading = false;\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          this.requirements = response.Entries.map(item => ({\r\n            ...item,\r\n            selected: false\r\n          }));\r\n        } else {\r\n          this.requirements = [];\r\n        }\r\n      },\r\n      error: () => {\r\n        this.loading = false;\r\n        this.requirements = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  onRequirementItemChange() {\r\n    // 當需求項目選擇變更時的處理\r\n  }\r\n\r\n  getSelectedItems(): ExtendedRequirementItem[] {\r\n    return this.requirements.filter(item => item.selected);\r\n  }\r\n\r\n  canProceed(): boolean {\r\n    switch (this.currentStep) {\r\n      case 1:\r\n        return this.getSelectedItems().length > 0;\r\n      case 2:\r\n        return true;\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  nextStep() {\r\n    if (this.canProceed() && this.currentStep < 2) {\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  previousStep() {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  getProgressText(): string {\r\n    const progressTexts = {\r\n      1: '請選擇要匯入的需求項目',\r\n      2: '確認匯入詳情'\r\n    };\r\n    return progressTexts[this.currentStep as keyof typeof progressTexts] || '';\r\n  }\r\n\r\n  importRequirements() {\r\n    const config: RequestItemImportConfig = {\r\n      buildCaseId: this.searchRequest.CBuildCaseID || 0,\r\n      buildCaseName: '',\r\n      selectedItems: this.getSelectedItems(),\r\n      totalItems: this.getSelectedItems().length,\r\n      searchCriteria: {\r\n        CHouseType: this.searchRequest.CHouseType || undefined,\r\n        CLocation: this.searchRequest.CLocation || undefined,\r\n        CRequirement: this.searchRequest.CRequirement || undefined\r\n      }\r\n    };\r\n\r\n    this.itemsImported.emit(config);\r\n    this.close();\r\n  }\r\n\r\n  close() {\r\n    this.resetSelections();\r\n    this.dialogRef.close();\r\n  }\r\n\r\n  private resetSelections() {\r\n    this.currentStep = 1;\r\n    this.requirements.forEach(requirement => {\r\n      requirement.selected = false;\r\n    });\r\n  }\r\n\r\n  selectAll() {\r\n    const allSelected = this.requirements.every(item => item.selected);\r\n    this.requirements.forEach(item => {\r\n      item.selected = !allSelected;\r\n    });\r\n  }\r\n\r\n  getSelectedCount(): number {\r\n    return this.getSelectedItems().length;\r\n  }\r\n\r\n  getTotalCount(): number {\r\n    return this.requirements.length;\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAAgB,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SACEC,YAAY,EACZC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,cAAc,EACdC,aAAa,QAER,gBAAgB;AAqChB,IAAMC,0BAA0B,GAAhC,MAAMA,0BAA0B;EAYrCC,YACUC,kBAAsC,EACtCC,SAAkD;IADlD,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,SAAS,GAATA,SAAS;IAbV,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,SAAS,GAAa,EAAE;IACvB,KAAAC,aAAa,GAAG,IAAIjB,YAAY,EAA2B;IAErE,KAAAkB,WAAW,GAAW,CAAC;IACvB,KAAAC,YAAY,GAA8B,EAAE;IAC5C,KAAAC,OAAO,GAAY,KAAK;IAExB;IACA,KAAAC,aAAa,GAAyF,EAAE;EAKpG;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEA;EACAD,oBAAoBA,CAAA;IAClB,IAAI,CAACF,aAAa,CAACI,OAAO,GAAG,CAAC,CAAC,CAAC;IAChC,IAAI,CAACJ,aAAa,CAACK,OAAO,GAAG,IAAI,CAAC,CAAC;IACnC,IAAI,CAACL,aAAa,CAACM,SAAS,GAAG,IAAI;IACnC,IAAI,CAACN,aAAa,CAACO,YAAY,GAAG,EAAE;IACpC,IAAI,CAACP,aAAa,CAACQ,SAAS,GAAG,EAAE;IACjC;IACA,IAAI,CAACR,aAAa,CAACS,YAAY,GAAG,IAAI,CAACf,WAAW;IAClD,IAAI,CAACM,aAAa,CAACU,UAAU,GAAG,IAAI,CAACf,SAAS;EAChD;EAIA;EACAgB,QAAQA,CAAA;IACN,IAAI,CAACR,uBAAuB,EAAE;EAChC;EAEA;EACAS,WAAWA,CAAA;IACT,IAAI,CAACV,oBAAoB,EAAE;IAC3B,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAA,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACH,aAAa,CAACS,YAAY,EAAE;MACpC;IACF;IAEA,IAAI,CAACV,OAAO,GAAG,IAAI;IAEnB;IACA,MAAMc,sBAAsB,GAA8B;MACxDJ,YAAY,EAAE,IAAI,CAACT,aAAa,CAACS,YAAY;MAC7CC,UAAU,EAAE,IAAI,CAACV,aAAa,CAACU,UAAU;MACzCF,SAAS,EAAE,IAAI,CAACR,aAAa,CAACQ,SAAS,IAAI,IAAI;MAC/CD,YAAY,EAAE,IAAI,CAACP,aAAa,CAACO,YAAY,IAAI,IAAI;MACrDH,OAAO,EAAE,IAAI,CAACJ,aAAa,CAACI,OAAO;MACnCC,OAAO,EAAE,IAAI,CAACL,aAAa,CAACK,OAAO;MACnCC,SAAS,EAAE,IAAI,CAACN,aAAa,CAACM,SAAS;MACvCQ,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;KACX;IAED,IAAI,CAACvB,kBAAkB,CAACwB,gDAAgD,CAAC;MACvEC,IAAI,EAAEJ;KACP,CAAC,CAACK,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAwC,IAAI;QACjD,IAAI,CAACrB,OAAO,GAAG,KAAK;QACpB,IAAIqB,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD,IAAI,CAACxB,YAAY,GAAGsB,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAChD,GAAGA,IAAI;YACPC,QAAQ,EAAE;WACX,CAAC,CAAC;QACL,CAAC,MAAM;UACL,IAAI,CAAC3B,YAAY,GAAG,EAAE;QACxB;MACF,CAAC;MACD4B,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC3B,OAAO,GAAG,KAAK;QACpB,IAAI,CAACD,YAAY,GAAG,EAAE;MACxB;KACD,CAAC;EACJ;EAEA6B,uBAAuBA,CAAA;IACrB;EAAA;EAGFC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC9B,YAAY,CAAC+B,MAAM,CAACL,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC;EACxD;EAEAK,UAAUA,CAAA;IACR,QAAQ,IAAI,CAACjC,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAAC+B,gBAAgB,EAAE,CAACG,MAAM,GAAG,CAAC;MAC3C,KAAK,CAAC;QACJ,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACF,UAAU,EAAE,IAAI,IAAI,CAACjC,WAAW,GAAG,CAAC,EAAE;MAC7C,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAoC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACpC,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAqC,eAAeA,CAAA;IACb,MAAMC,aAAa,GAAG;MACpB,CAAC,EAAE,aAAa;MAChB,CAAC,EAAE;KACJ;IACD,OAAOA,aAAa,CAAC,IAAI,CAACtC,WAAyC,CAAC,IAAI,EAAE;EAC5E;EAEAuC,kBAAkBA,CAAA;IAChB,MAAMC,MAAM,GAA4B;MACtC3C,WAAW,EAAE,IAAI,CAACM,aAAa,CAACS,YAAY,IAAI,CAAC;MACjD6B,aAAa,EAAE,EAAE;MACjBC,aAAa,EAAE,IAAI,CAACX,gBAAgB,EAAE;MACtCY,UAAU,EAAE,IAAI,CAACZ,gBAAgB,EAAE,CAACG,MAAM;MAC1CU,cAAc,EAAE;QACd/B,UAAU,EAAE,IAAI,CAACV,aAAa,CAACU,UAAU,IAAIgC,SAAS;QACtDlC,SAAS,EAAE,IAAI,CAACR,aAAa,CAACQ,SAAS,IAAIkC,SAAS;QACpDnC,YAAY,EAAE,IAAI,CAACP,aAAa,CAACO,YAAY,IAAImC;;KAEpD;IAED,IAAI,CAAC9C,aAAa,CAAC+C,IAAI,CAACN,MAAM,CAAC;IAC/B,IAAI,CAACO,KAAK,EAAE;EACd;EAEAA,KAAKA,CAAA;IACH,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACpD,SAAS,CAACmD,KAAK,EAAE;EACxB;EAEQC,eAAeA,CAAA;IACrB,IAAI,CAAChD,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,YAAY,CAACgD,OAAO,CAACC,WAAW,IAAG;MACtCA,WAAW,CAACtB,QAAQ,GAAG,KAAK;IAC9B,CAAC,CAAC;EACJ;EAEAuB,SAASA,CAAA;IACP,MAAMC,WAAW,GAAG,IAAI,CAACnD,YAAY,CAACoD,KAAK,CAAC1B,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC;IAClE,IAAI,CAAC3B,YAAY,CAACgD,OAAO,CAACtB,IAAI,IAAG;MAC/BA,IAAI,CAACC,QAAQ,GAAG,CAACwB,WAAW;IAC9B,CAAC,CAAC;EACJ;EAEAE,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACvB,gBAAgB,EAAE,CAACG,MAAM;EACvC;EAEAqB,aAAaA,CAAA;IACX,OAAO,IAAI,CAACtD,YAAY,CAACiC,MAAM;EACjC;CACD;AAzKUsB,UAAA,EAARzE,KAAK,EAAE,C,8DAAyB;AACxByE,UAAA,EAARzE,KAAK,EAAE,C,4DAA0B;AACxByE,UAAA,EAATxE,MAAM,EAAE,C,gEAA6D;AAH3DS,0BAA0B,GAAA+D,UAAA,EAjBtC3E,SAAS,CAAC;EACT4E,QAAQ,EAAE,yBAAyB;EACnCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP1E,YAAY,EACZC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdwE,YAAY,EACZvE,gBAAgB,EAChBC,cAAc,EACdC,cAAc,EACdC,aAAa,CACd;EACDqE,WAAW,EAAE,sCAAsC;EACnDC,SAAS,EAAE,CAAC,sCAAsC;CACnD,CAAC,C,EACWrE,0BAA0B,CA0KtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}